import os
import logging
import json
import uuid
import time
from flask import Flask, Blueprint, request, jsonify
from dotenv import load_dotenv
import paho.mqtt.client as mqtt
from openai import OpenAI
import threading
from datetime import datetime, timedelta
import re

from bot.routes import blueprint as messenger_bot_blueprint
from bot.config import get_openai_api_key, get_mqtt_config, get_api_base_url
from bot.authentication import user_sessions
from bot.utils import send_message
import requests

# Load environment variables
load_dotenv()
print("==== ASLAA BOT STARTUP ====")
print("Environment variables loaded")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logge= logging.getLogger(__name__)
logger.info("==== ASLAA BOT STARTUP ====")
logger.info("Logging configured")

# Initialize OpenAI client
client = None
try:
    logger.info("Initializing OpenAI client...")
    # For OpenAI 1.3.0, remove any proxies parameter
    client = OpenAI(api_key=get_openai_api_key())
    logger.info("✅ OpenAI client initialized successfully")
except Exception as e:
    logger.error(f"❌ Failed to initialize OpenAI client: {e}")

# Initialize MQTT client
mqtt_client = None
try:
    logger.info("Initializing MQTT client...")
    mqtt_config = get_mqtt_config()

    # Use the newer Client API version to avoid deprecation warning
    mqtt_client = mqtt.Client(
        client_id=f"fb_messenger_bot_{uuid.uuid4()}",
        protocol=mqtt.MQTTv311,
        callback_api_version=mqtt.CallbackAPIVersion.VERSION2
    )
    mqtt_client.username_pw_set(mqtt_config["username"], mqtt_config["password"])

    # Set up MQTT callbacks
    def on_connect(client, userdata, flags, reason_code, properties=None):
        if isinstance(reason_code, int):  # For backward compatibility
            rc = reason_code
            if rc == 0:
                logger.info(f"✅ Connected to MQTT broker successfully")

                # Subscribe to system topics only
                # We'll subscribe to device-specific topics after authentication
                client.subscribe("$SYS/#")  # System topics
                logger.info("✅ Subscribed to MQTT system topics")

                # Subscribe to device topics for already authenticated users
                for session in user_sessions.values():
                    if session.get("state") == "AUTHENTICATED" and session.get("deviceNumber"):
                        device_number = session.get("deviceNumber")
                        client.subscribe(f"{device_number}")
                        client.subscribe(f"{device_number}/msg")
                        logger.info(f"✅ Resubscribed to device topics: {device_number} and {device_number}/msg")
            else:
                logger.error(f"❌ Failed to connect to MQTT broker with result code {rc}")
        else:  # VERSION2 API
            if reason_code.is_failure:
                logger.error(f"❌ Failed to connect to MQTT broker: {reason_code}")
            else:
                logger.info(f"✅ Connected to MQTT broker successfully")

                # Subscribe to system topics only
                # We'll subscribe to device-specific topics after authentication
                client.subscribe("$SYS/#")  # System topics
                logger.info("✅ Subscribed to MQTT system topics")

                # Subscribe to device topics for already authenticated users
                for session in user_sessions.values():
                    if session.get("state") == "AUTHENTICATED" and session.get("deviceNumber"):
                        device_number = session.get("deviceNumber")
                        client.subscribe(f"{device_number}")
                        client.subscribe(f"{device_number}/msg")
                        logger.info(f"✅ Resubscribed to device topics: {device_number} and {device_number}/msg")

    def on_message(client, userdata, msg):
        try:
            # Parse the message
            topic = msg.topic
            payload = json.loads(msg.payload.decode())

            logger.info(f"📥 Received MQTT message - Topic: {topic}, Payload: {payload}")

            # Extract device ID from topic (format: {device_id}/msg or just {device_id})
            parts = topic.split('/')
            device_id = parts[0]  # The device ID is always the first part

            # Log the received message
            logger.info(f"📥 Received message from device {device_id}: {payload}")

            # Update the latest device data
            update_latest_device_data(device_id, payload)

            # If this is a device_id/msg topic with voltage data, it's likely a response to a command
            if len(parts) >= 2 and parts[1] == "msg" and "volt" in payload:
                # Mark all pending commands for this device as having received a response
                pending_commands = [tid for tid in command_tracking.keys() if tid.startswith(f"{device_id}:")]

                for tracking_id in pending_commands:
                    command = tracking_id.split(":", 1)[1]
                    logger.info(f"✅ Received response for command '{command}' to device {device_id} via msg topic with voltage data")
                    command_tracking[tracking_id]["response_received"] = True

                    # Format a response message based on the command and voltage
                    try:
                        # Find Facebook users associated with this device
                        for sender_id, session in user_sessions.items():
                            if session.get("deviceNumber") == device_id and session.get("state") == "AUTHENTICATED":
                                # Format a response based on the command
                                if command == "as":
                                    # Check if the engine is on based on voltage
                                    try:
                                        volt_value = float(payload["volt"])
                                        if volt_value > 13.5:
                                            response_message = "✅ Машин амжилттай асаалаа.\n🟢 Асаалттай | ⚡ Хүчдэл: {:.2f}V".format(volt_value)
                                        else:
                                            response_message = "⚠️ Машин асаагүй байж магадгүй.\n🔴 Унтраалттай | ⚡ Хүчдэл: {:.2f}V".format(volt_value)
                                    except:
                                        response_message = "✅ Машины хариу ирлээ."
                                elif command == "untar":
                                    # Check if the engine is off based on voltage
                                    try:
                                        volt_value = float(payload["volt"])
                                        if volt_value < 13.5:
                                            response_message = "✅ Машин амжилттай унтарлаа.\n🔴 Унтраалттай | ⚡ Хүчдэл: {:.2f}V".format(volt_value)
                                        else:
                                            response_message = "⚠️ Машин унтраагүй байж магадгүй.\n🟢 Асаалттай | ⚡ Хүчдэл: {:.2f}V".format(volt_value)
                                    except:
                                        response_message = "✅ Машины хариу ирлээ."
                                elif command == "check":
                                    # Format a status message with location and voltage
                                    response_message = format_status_update(payload)
                                else:
                                    # Generic response for other commands
                                    response_message = f"✅ '{get_friendly_command_name(command)}' команд амжилттай гүйцэтгэгдлээ."

                                # Send the message to the user
                                send_message(sender_id, response_message)
                    except Exception as e:
                        logger.error(f"Error formatting response message: {e}")

                    # Remove the command from tracking
                    command_tracking.pop(tracking_id, None)

            # Check if this is a response to a command on the direct device topic
            command = payload.get("command")
            if command and len(parts) == 1:
                # Create the tracking ID
                tracking_id = f"{device_id}:{command}"

                # Mark the command as having received a response
                if tracking_id in command_tracking:
                    logger.info(f"✅ Received response for command '{command}' to device {device_id}")
                    command_tracking[tracking_id]["response_received"] = True
                    # Remove the command from tracking
                    command_tracking.pop(tracking_id, None)

            # Process the message based on the topic format for user notifications
            if len(parts) >= 2 and parts[1] == "msg":
                # This is a {device_id}/msg topic format

                # Find Facebook users associated with this device
                for sender_id, session in user_sessions.items():
                    if session.get("deviceNumber") == device_id and session.get("state") == "AUTHENTICATED":
                        # Check if this is a response to a command or just a periodic update
                        is_command_response = "message" in payload

                        # Only send a message if this is not already handled as a command response above
                        # and it's a periodic update (not a command response)
                        if not is_command_response and not any(tid.startswith(f"{device_id}:") for tid in command_tracking.keys()):
                            # This is a periodic status update - make it shorter
                            response_message = format_status_update(payload)

                            # Send the message to the user
                            send_message(sender_id, response_message)
        except Exception as e:
            logger.error(f"Error processing MQTT message: {e}")
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")

    def format_device_response(payload):
        """
        Format a device response message in a user-friendly way
        """
        # Check if there's a message field
        if "message" in payload:
            message = payload["message"]

            # Check if this is an error message
            if "error" in message.lower() or "failed" in message.lower():
                return f"❌ {message}"

            # Otherwise, it's a success message
            return f"✅ {message}"

        # If there's no message field, format based on the command
        if "command" in payload:
            command = payload["command"]
            return format_command_response(command, payload)

        # Default formatting for unknown response types
        return f"📱 Хариу: {json.dumps(payload, ensure_ascii=False)}"

    def format_status_update(data):
        """
        Format a status update message from device data - shorter version without location link
        """
        message = "🚗 Машины төлөв: "

        # Add engine status based on voltage
        if "volt" in data:
            try:
                volt_value = float(data["volt"])
                if volt_value > 13.5:
                    message += "🟢 Асаалттай"
                else:
                    message += "🔴 Унтраалттай"
                message += f" | ⚡ {volt_value:.2f}V"
            except:
                message += f" | ⚡ {data.get('volt', 'N/A')}"

        # Add temperature if available
        if "temp" in data:
            message += f" | 🌡️ {data['temp']}°C"

        # Add speed if available (only if moving)
        if "Speed" in data:
            try:
                speed_value = int(data["Speed"])
                if speed_value > 0:
                    message += f" | 🚗 {speed_value} км/ц"
            except:
                pass

        return message

    def format_command_response(command, payload):
        """
        Format a response message based on the command and payload
        """
        if command == "check":
            # Format check command response - shorter version
            return format_status_update(payload)

        elif command == "sim":
            # Format SIM command response
            message = "📱 SIM картын мэдээлэл:\n\n"

            # Add SIM status if available
            if "status" in payload:
                message += f"Төлөв: {payload['status']}\n"

            # Add balance if available
            if "balance" in payload:
                message += f"Үлдэгдэл: {payload['balance']}₮\n"

            # Add expiry if available
            if "expiry" in payload:
                message += f"Хугацаа: {payload['expiry']}\n"
            elif "expiredDate" in payload:
                message += f"Хугацаа: {payload['expiredDate']}\n"

            # Add SMS text if available
            if "sms" in payload:
                message += f"\nСМС: {payload['sms']}\n"

            return message

        elif command == "asa":
            # Format engine start response
            return "✅ Машин амжилттай асаалаа."

        elif command == "as":
            # Format engine stop response
            return "✅ Машин амжилттай унтарлаа."

        elif command == "lock":
            # Format lock response
            return "🔒 Машин амжилттай түгжигдлээ."

        elif command == "unlock":
            # Format unlock response
            return "🔓 Машин нээлтийн дугаар амжилттай нээгдлээ."

        elif command == "mirror":
            # Format mirror adjustment response
            return "🔄 Толь амжилттай тохируулагдлаа."

        elif command == "restart":
            # Format restart response
            return "🔄 Төхөөрөмж дахин эхлүүлж байна. Энэ үйлдэл 1-2 минут үргэлжилнэ."

        elif command == "update":
            # Format update response
            return "📥 Шинэчлэл эхэллээ. Энэ үйлдэл 3-5 минут үргэлжилнэ."

        elif command == "version":
            # Format version response
            if "version" in payload:
                return f"📱 Firmware хувилбар: {payload['version']}"
            else:
                return "📱 Firmware хувилбарын мэдээлэл авах хүсэлт илгээгдлээ."

        elif command.startswith("auto_shutdown"):
            # Handle auto_shutdown commands
            if command == "auto_shutdown on":
                return "⏱️ Автомат унтраалт идэвхжүүллээ. Машин 30 минутын дараа автоматаар унтарна."
            elif command == "auto_shutdown off":
                return "⏱️ Автомат унтраалт идэвхгүй болголоо."
            elif command == "auto_shutdown status":
                if "status" in payload:
                    return f"⏱️ {payload['status']}"
                else:
                    return "⏱️ Автомат унтраалтын төлөв шалгаж байна..."
            elif "auto_shutdown timer" in command:
                # Extract timer value
                timer_match = re.match(r'auto_shutdown timer (\d+)', command)
                if timer_match:
                    minutes = timer_match.group(1)
                    return f"⏱️ Автомат унтраалтын хугацааг {minutes} минут болгож тохируулав."
                else:
                    return "⏱️ Автомат унтраалтын хугацааг тохируулах гэж оролдлоо."

        elif command == "geely_atlas on":
            # Format Geely Atlas enable response
            return "🚙 Geely Atlas горим амжилттай идэвхжүүллээ."

        elif command == "geely_atlas off":
            # Format Geely Atlas disable response
            return "🚙 Geely Atlas горим амжилттай идэвхгүй болголоо."

        elif command == "geely_atlas status":
            # Format Geely Atlas status response
            if "status" in payload:
                return f"🚙 Geely Atlas горим: {payload['status']}"
            else:
                return "🚙 Geely Atlas горимын төлөв шалгаж байна..."

        elif command == "notify on":
            # Format notify enable response
            return "🔔 Хүчдэлийн мэдэгдэл амжилттай идэвхжүүллээ. Хүчдэл 0.5В-оор өөрчлөгдөх бүрт мэдэгдэл ирнэ."

        elif command == "notify off":
            # Format notify disable response
            return "🔕 Хүчдэлийн мэдэгдэл амжилттай идэвхгүй болголоо."

        elif threshold_match := re.match(r'threshold(\d+\.?\d*)', command):
            # Format threshold response with the value
            threshold_value = threshold_match.group(1)
            return f"⚡ Хүчдэлийн босго {threshold_value}В болгож тохируулагдлаа."

        elif volt_match := re.match(r'volt(\d+\.?\d*)', command):
            # Format volt offset response with the value
            volt_value = volt_match.group(1)
            return f"⚡ Хүчдэлийн засвар {volt_value}В болгож тохируулагдлаа."

        else:
            # For other commands, return a generic success message
            return "✅ Команд амжилттай илгээгдлээ."

    # Set the callbacks
    mqtt_client.on_connect = on_connect
    mqtt_client.on_message = on_message

    # Connect to the broker
    logger.info(f"Connecting to MQTT broker at {mqtt_config['host']}:{mqtt_config['port']}...")
    mqtt_client.connect(mqtt_config["host"], mqtt_config["port"])
    mqtt_client.loop_start()
    logger.info("✅ MQTT client initialized and connection started")
except Exception as e:
    logger.error(f"❌ Failed to initialize MQTT client: {e}")
    import traceback
    logger.error(f"Detailed error: {traceback.format_exc()}")

# In-memory conversation contexts
conversation_contexts = {}
logger.info("Initialized conversation contexts storage")

# System prompt for the assistant
ASLAA_SYSTEM_PROMPT = """
Та Aslaa туслах бот. Та хэрэглэгчдэд машинаа удирдахад туслах зорилготой.
You are the Aslaa Assistant bot. You help users control their cars.

Та Монгол болон Англи хэл дээр харилцах чадвартай.
You can communicate in both Mongolian and English.

IMPORTANT: Only send commands when the user EXPLICITLY asks for a specific action.
Do not send commands for general questions or inquiries about the car's status.

For example:
- If user asks "машин дотор хүн байна уу?" (is there someone in the car?) - DO NOT send any command, just explain you can't check that
- If user asks "доторх температур хэд вэ?" (what's the interior temperature?) - You can use the "check" command to get status
- If user asks "машин асаах" (turn on the car) - Use the "as" command
- If user asks "негж" or "негж байна уу" or "negj" (check SIM balance) - Use the "sim" command to check SIM card status
- If user asks "машин асахгүй байна" (car won't start) - DO NOT send any command, just provide troubleshooting advice

TROUBLESHOOTING PHRASES (DO NOT SEND COMMANDS FOR THESE):
- "асахгүй" or "assangvi" - means "won't start"
- "ажиллахгүй" - means "not working"
- "юутай холбоотой" or "yutai holbootoi" - means "what's the reason"
- "яагаад" - means "why"

Боломжтой командууд / Available commands:
- as: Машин асаах / Turn on the car engine
- untar: Машин унтраах / Turn off the car engine
- lock: Машин түгжих / Lock the car doors
- unlock: Машин нээх / Unlock the car doors
- check: Машины төлөв шалгах / Check the current status of the car
- mirror: Толь тохируулах / Adjust mirrors
- restart: Төхөөрөмж дахин эхлүүлэх / Restart the device
- update: Төхөөрөмжийн программ хангамж шинэчлэх / Update the device software
- sim: SIM картын төлөв шалгах / Check SIM card status
- id: Төхөөрөмжийн ID авах / Get device ID
- sound on: Дуу асаах / Turn on sound
- sound off: Дуу унтраах / Turn off sound
- display on: Дэлгэц асаах / Turn on display
- display off: Дэлгэц унтраах / Turn off display
- geely_atlas on: Geely Atlas горим асаах / Enable Geely Atlas mode
- geely_atlas off: Geely Atlas горим унтраах / Disable Geely Atlas mode
- geely_atlas status: Geely Atlas горим шалгах / Check Geely Atlas mode status
- notify on: Хүчдэлийн мэдэгдэл асаах / Enable voltage change notifications
- notify off: Хүчдэлийн мэдэгдэл унтраах / Disable voltage change notifications
- threshold13.5: Хүчдэлийн босго 13.5В болгох / Set voltage threshold to 13.5V
- volt1.2: Хүчдэлийн засвар 1.2В болгох / Set voltage offset to 1.2V

COMMON MONGOLIAN PHRASES:
- "доторх" or "дотор" - means "inside" or "interior"
- "байна уу" - means "is there" or "is it"
- "хүн" - means "person"
- "негж" or "negj" - means "SIM balance" or "check SIM balance" - use "sim" command for this
- "машинд" - means "in the car"

When a user gives a command, use the send_mqtt_command function to send the command to their car.
"""
logger.info("System prompt configured")

# Tool definitions
MQTT_TOOL = {
    "type": "function",
    "function": {
        "name": "send_mqtt_command",
        "description": "Send a command to the user's car via MQTT",
        "parameters": {
            "type": "object",
            "properties": {
                "command": {
                    "type": "string",
                    "description": "The command to send (e.g., 'untar', 'as', 'lock', 'unlock')",
                    "enum": [
                        "as", "untar", "lock", "unlock", "check", "mirror",
                        "restart", "update", "sim", "id", "sound on",
                        "sound off", "display on", "display off",
                        "key on", "key off", "version", "auto_shutdown on",
                        "auto_shutdown off", "auto_shutdown status",
                        "geely_atlas on", "geely_atlas off", "geely_atlas status",
                        "notify on", "notify off", "threshold", "volt"
                    ]
                },
                "device_number": {
                    "type": "string",
                    "description": "The device number of the car (optional if already provided in context)"
                },
                "timer_minutes": {
                    "type": "integer",
                    "description": "Minutes for auto_shutdown timer (only used with auto_shutdown timer command)"
                }
            },
            "required": ["command"]
        }
    }
}
logger.info("MQTT tool definition configured")

def authenticate_user(phone_number, pin):
    """
    Authenticate a user with their phone number and PIN
    """
    try:
        # Remove any spaces or special characters from phone number
        phone_number = ''.join(filter(str.isdigit, phone_number))

        # Call the authentication API
        url = f"{get_api_base_url()}/auth"
        response = requests.post(url, json={
            "phone_number": phone_number,
            "pin": pin
        })

        if response.status_code == 200:
            data = response.json()
            return {
                "success": True,
                "message": "Authentication successful",
                "token": data.get("token"),
                "device_number": data.get("device_number")
            }
        else:
            return {
                "success": False,
                "message": "Invalid phone number or PIN"
            }
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        return {
            "success": False,
            "message": f"Authentication error: {str(e)}"
        }

def send_command_to_device(device_number, command, token, retry_count=0):
    """
    Send a command to a device via MQTT
    """
    try:
        # Map common command variations to standard commands
        command_mapping = {
            "negj": "sim",
            "негж": "sim",
            "balance": "sim",
            "үлдэгдэл": "sim",
            "start": "as",
            "эхлүүлэх": "as",
            "stop": "untar",
            "зогсоох": "untar",
            "түгжих": "lock",
            "нээх": "unlock",
            "шалгах": "check",
            "status": "check",
            "төлөв": "check",
            "толь": "mirror",
            "толь эвхэнэ үү": "mirror",
            "толь эвхэх": "mirror",
            "цонх": "mirror",
            "цонх буулгана уу": "mirror",
            "цонх буулгах": "mirror",
            "дахин эхлүүлэх": "restart",
            "перезагрузка": "restart",
            "reboot": "restart",
            "шинэчлэх": "update",
            "шинэчлэл": "update",
            "firmware": "update",
            "хувилбар": "version",
            "version": "version",
            "автомат унтраалт асаах": "auto_shutdown on",
            "автомат унтраалт идэвхжүүлэх": "auto_shutdown on",
            "автомат унтраалт унтраах": "auto_shutdown off",
            "автомат унтраалт идэвхгүй болгох": "auto_shutdown off",
            "автомат унтраалт төлөв": "auto_shutdown status",
            "автомат унтраалт шалгах": "auto_shutdown status",
            "geely atlas асаах": "geely_atlas on",
            "geely atlas идэвхжүүлэх": "geely_atlas on",
            "geely atlas унтраах": "geely_atlas off",
            "geely atlas идэвхгүй болгох": "geely_atlas off",
            "geely atlas төлөв": "geely_atlas status",
            "geely atlas шалгах": "geely_atlas status",
            "мэдэгдэл асаах": "notify on",
            "мэдэгдэл идэвхжүүлэх": "notify on",
            "мэдэгдэл унтраах": "notify off",
            "мэдэгдэл идэвхгүй болгох": "notify off",
            "notification on": "notify on",
            "notification off": "notify off"
        }

        # Check for auto_shutdown timer command
        auto_shutdown_timer_match = re.match(r'auto_shutdown timer (\d+)', command)
        # Check for threshold command with value
        threshold_match = re.match(r'threshold(\d+\.?\d*)', command)
        # Check for volt command with value
        volt_match = re.match(r'volt(\d+\.?\d*)', command)

        if auto_shutdown_timer_match:
            normalized_command = command  # Keep the full command with timer value
        elif threshold_match:
            normalized_command = command  # Keep the full threshold command with value
        elif volt_match:
            normalized_command = command  # Keep the full volt command with value
        else:
            # Normalize the command
            normalized_command = command_mapping.get(command.lower(), command.lower())

        # Create a unique tracking ID for this command
        tracking_id = f"{device_number}:{normalized_command}"

        # Create the command payload
        payload = {
            "id": device_number,
            "command": normalized_command
        }

        # Convert to JSON
        payload_json = json.dumps(payload)

        # Track the command
        command_tracking[tracking_id] = {
            "device_number": device_number,
            "command": normalized_command,
            "token": token,
            "retry_count": retry_count,
            "timestamp": time.time(),
            "response_received": False
        }

        # Send the command via MQTT
        if mqtt_client:
            topic = f"{device_number}"
            logger.info(f"Sending command to topic {topic}: {payload_json}")
            result = mqtt_client.publish(topic, payload_json)

            # Check if the publish was successful
            if result.rc == 0:
                logger.info(f"✅ Command sent successfully: {normalized_command} to {device_number}")

                # Schedule a check for response after 10 seconds
                threading.Timer(10.0, check_command_response, args=[tracking_id]).start()

                return True
            else:
                logger.error(f"❌ Failed to send command: {result.rc}")
                # Remove the command from tracking
                command_tracking.pop(tracking_id, None)
                return False
        else:
            logger.error("❌ MQTT client not initialized")
            # Remove the command from tracking
            command_tracking.pop(tracking_id, None)
            return False
    except Exception as e:
        logger.error(f"❌ Error sending command: {e}")
        import traceback
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return False

def get_friendly_command_name(command):
    """
    Get a friendly name for a command
    """
    command_names = {
        "as": "Асаах",
        "untar": "Унтраах",
        "lock": "Түгжих",
        "unlock": "Түгжээ тайлах",
        "check": "Шалгах",
        "mirror": "Толь",
        "restart": "Дахин эхлүүлэх",
        "update": "Шинэчлэх",
        "sim": "Симийн үлдэгдэл",
        "id": "ID",
        "sound on": "Дуу асаах",
        "sound off": "Дуу унтраах",
        "display on": "Дэлгэц асаах",
        "display off": "Дэлгэц унтраах",
        "key on": "Түлхүүр асаах",
        "key off": "Түлхүүр унтраах",
        "version": "Хувилбар",
        "geely_atlas on": "Geely Atlas горим асаах",
        "geely_atlas off": "Geely Atlas горим унтраах",
        "geely_atlas status": "Geely Atlas горим шалгах",
        "notify on": "Мэдэгдэл асаах",
        "notify off": "Мэдэгдэл унтраах",
        "threshold": "Хүчдэлийн босго тохируулах",
        "volt": "Хүчдэлийн засвар тохируулах"
    }

    return command_names.get(command, command)

def process_facebook_message(sender_id, text):
    """
    Process a message from Facebook Messenger
    """
    logger.info(f"Processing Facebook message: sender_id={sender_id}, text='{text}'")

    # Get the user session
    session = user_sessions.get(sender_id, {"state": "GREET"})

    # If the user is not authenticated or is in a payment flow, handle authentication flow
    payment_states = ["SELECT_EXTENSION_PERIOD", "CONFIRM_EXTENSION", "PAYMENT_METHOD", "PAYMENT_PENDING"]
    if session["state"] != "AUTHENTICATED" or session["state"] in payment_states:
        # Handle authentication flow (existing code)
        from bot.authentication import handle_auth_flow
        handle_auth_flow(sender_id, text)
        return

    # If the user is authenticated, process the command
    device_number = session.get("deviceNumber")
    token = session.get("token")

    if not device_number or not token:
        send_message(sender_id, "Таны мэдээлэл дутуу байна. Дахин нэвтэрнэ үү.")
        session["state"] = "GREET"
        user_sessions[sender_id] = session
        return

    # Check if this is a license extension command
    extension_commands = ["extend", "сунгах", "sungah", "license", "лиценз"]
    if any(cmd in text.lower() for cmd in extension_commands):
        logger.info(f"License extension command detected: {text}")
        from bot.authentication import handle_extend_license
        handle_extend_license(sender_id)
        return

    # Check if this is a help request
    is_help_request = any(keyword in text.lower() for keyword in ["help", "тусламж", "tuslamj", "commands", "команд", "komand"])
    if is_help_request:
        # Show available commands
        help_message = "📋 Боломжтой командууд:\n\n"
        help_message += "🚗 Машин удирдах:\n"
        help_message += "- as: Машин асаах\n"
        help_message += "- untar: Машин унтраах\n"
        help_message += "- lock: Машин түгжих\n"
        help_message += "- unlock: Машин нээх\n"
        help_message += "- check: Машины төлөв шалгах\n\n"

        help_message += "📱 Бусад:\n"
        help_message += "- sim: SIM картын үлдэгдэл шалгах\n"
        help_message += "- restart: Төхөөрөмж дахин эхлүүлэх\n"
        help_message += "- extend: Үйлчилгээний хугацаа сунгах\n"
        help_message += "- logout: Системээс гарах\n\n"

        help_message += "🚙 Geely Atlas:\n"
        help_message += "- geely_atlas on: Geely Atlas горим асаах\n"
        help_message += "- geely_atlas off: Geely Atlas горим унтраах\n"
        help_message += "- geely_atlas status: Geely Atlas горим шалгах\n\n"

        help_message += "⚡ Хүчдэлийн мэдэгдэл:\n"
        help_message += "- notify on: Хүчдэлийн мэдэгдэл асаах\n"
        help_message += "- notify off: Хүчдэлийн мэдэгдэл унтраах\n"
        help_message += "- threshold13.5: Хүчдэлийн босго 13.5В болгох\n"
        help_message += "- volt1.2: Хүчдэлийн засвар 1.2В болгох\n"

        send_message(sender_id, help_message)
        return

    # Check if this is a logout request
    is_logout_request = any(keyword in text.lower() for keyword in ["logout", "гарах", "garakh", "гарч", "гарчих", "log out"])
    if is_logout_request:
        # Handle logout
        handle_logout(sender_id)
        return

    # Check if this is a SIM balance request
    is_sim_request = any(keyword in text.lower() for keyword in ["негж", "negj"])
    if is_sim_request:
        # Use the improved SIM status check with fallback
        send_message(sender_id, "SIM картын үлдэгдлийг шалгаж байна... Хариу ирэхийг хүлээнэ үү.")
        check_sim_status(device_number, token)
        return

    # Check for troubleshooting keywords
    troubleshooting_keywords = [
        "асахгүй", "хариу өгөхгүй", "ажиллахгүй", "асуудалтай",
        "проблем", "алдаа", "not working", "problem", "issue", "help",
        "туслаач", "тусална уу", "ассангүй", "юутай", "holbootoi",
        "холбоотой", "яагаад"
    ]

    is_troubleshooting = any(keyword in text.lower() for keyword in troubleshooting_keywords)

    # If this is a troubleshooting request, provide advice first
    if is_troubleshooting:
        advice = provide_troubleshooting_advice(text, device_number, token)
        send_message(sender_id, advice)

        # Also check SIM status with improved fallback
        send_message(sender_id, "SIM картын төлөвийг шалгаж байна... Хариу ирэхийг хүлээнэ үү.")
        check_sim_status(device_number, token)

        # Also check car status
        send_command_to_device(device_number, "check", token)
        send_message(sender_id, "Машины төлөвийг шалгаж байна... Хариу ирэхийг хүлээнэ үү.")
        return

    # Check for location-specific keywords
    location_keywords = ["хаана", "байршил", "location", "where", "haana", "bairshil", "mashin haana", "машин хаана"]
    is_location_request = any(keyword in text.lower() for keyword in location_keywords)

    # If this is a location request, handle it directly
    if is_location_request:
        # Check if we have recent location data
        location_info = get_specific_device_info(device_number, "location")
        if location_info:
            send_message(sender_id, f"🚗 Машины байршил:\n{location_info}")

        # Always send a check command to get the latest location
        send_command_to_device(device_number, "check", token)
        if not location_info:
            send_message(sender_id, "Машины байршлыг шалгаж байна... Хариу ирэхийг хүлээнэ үү.")
        return

    # Check for other specific information requests
    info_request = check_specific_info_request(text, device_number, token)
    if info_request:
        send_message(sender_id, info_request["message"])
        if info_request.get("command"):
            # Send the appropriate command to get updated info
            send_command_to_device(device_number, info_request["command"], token)
        return

    # Check for common phrases that should NOT trigger commands
    safe_phrases = [
        "дотор", "доторх", "байна уу", "хүн", "машинд",
        "inside", "interior", "is there", "person", "check if"
    ]

    is_safe_query = any(phrase in text.lower() for phrase in safe_phrases)

    # Create a conversation ID for this Facebook user
    conversation_id = f"fb_{sender_id}"

    # Store the context in conversation contexts
    if conversation_id not in conversation_contexts:
        conversation_contexts[conversation_id] = {
            "messages": [],
            "device_number": device_number,
            "token": token
        }

    # Add the user message to the conversation history
    conversation_contexts[conversation_id]["messages"].append({"role": "user", "content": text})

    # Process the message with the LLM
    if client:
        try:
            # Create system message with context
            system_message = ASLAA_SYSTEM_PROMPT

            # Add context information
            system_message += f"\n\nCONTEXT:\nThe user's device number is: {device_number}"
            system_message += "\nThe user is authenticated."

            # If this is a safe query about the car interior, add special instructions
            if is_safe_query:
                system_message += "\nIMPORTANT: The user is asking about the car interior or checking if someone is in the car. DO NOT send any restart or other commands. Just explain what information you can provide."

            # Prepare messages for the API call
            messages = [{"role": "system", "content": system_message}]

            # Add conversation history (limited to last 10 messages)
            history = conversation_contexts[conversation_id]["messages"][-10:]
            messages.extend(history)

            logger.info(f"Sending message to OpenAI for user {sender_id}: '{text}'")

            # Call the LLM with tool definition
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                tools=[MQTT_TOOL],
                tool_choice="auto"
            )

            # Get the response
            message = response.choices[0].message

            # Check if the model wants to use a tool
            if message.tool_calls:
                # Extract tool call details
                tool_call = message.tool_calls[0]
                function_name = tool_call.function.name

                # Handle MQTT command
                if function_name == "send_mqtt_command":
                    # Parse function arguments
                    try:
                        function_args = json.loads(tool_call.function.arguments)
                        command = function_args.get("command")
                        cmd_device_number = function_args.get("device_number", device_number)

                        # Validate command and device number
                        if not command:
                            raise ValueError("Command is required")

                        if not cmd_device_number:
                            raise ValueError("Device number is required")

                        # SAFETY CHECK: If this is a potentially dangerous command and the message contains safe phrases
                        dangerous_commands = ["restart", "update"]
                        if command in dangerous_commands and is_safe_query:
                            # Override with a safer response
                            assistant_message = "Уучлаарай, таны асуултаас харахад та машины дотор талын мэдээлэл асууж байна. Би зөвхөн машины үндсэн мэдээллийг харуулах боломжтой. Машины төлөв шалгахыг хүсвэл 'check' гэж бичнэ үү."
                            conversation_contexts[conversation_id]["messages"].append({"role": "assistant", "content": assistant_message})
                            send_message(sender_id, assistant_message)
                            return

                        # Log the command being sent
                        logger.info(f"Sending command '{command}' to device {cmd_device_number} for user {sender_id}")

                        # If "негж" or "negj" is in the message, force the command to be "sim"
                        if any(keyword in text.lower() for keyword in ["негж", "negj"]) and command != "sim":
                            command = "sim"

                        # Send the command
                        success = send_command_to_device(cmd_device_number, command, token)

                        # Prepare response based on success
                        if success:
                            # Get a friendly name for the command
                            friendly_command = get_friendly_command_name(command)

                            # Use a more user-friendly response
                            assistant_message = f"✅ '{friendly_command}' команд амжилттай илгээгдлээ. Машины хариу ирэхийг хүлээнэ үү."
                        else:
                            assistant_message = f"❌ '{command}' команд илгээхэд алдаа гарлаа. Дахин оролдоно уу."

                    except Exception as e:
                        logger.error(f"Error processing command: {e}")
                        logger.error(f"Detailed error: {traceback.format_exc()}")
                        assistant_message = f"❌ Команд боловсруулахад алдаа гарлаа: {str(e)}"
                else:
                    assistant_message = f"❓ Тодорхойгүй функц: {function_name}"
            else:
                # Use the model's text response for general conversation
                assistant_message = message.content
                logger.info(f"Received general conversation response from OpenAI for user {sender_id}")

            # Add assistant message to conversation history
            conversation_contexts[conversation_id]["messages"].append({"role": "assistant", "content": assistant_message})

            # Send the response to the user
            send_message(sender_id, assistant_message)
            logger.info(f"Sent response to user {sender_id}: '{assistant_message}'")

        except Exception as e:
            logger.error(f"Error processing message with LLM: {e}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            send_message(sender_id, f"Error processing your message: {str(e)}")
    else:
        send_message(sender_id, "The AI assistant is currently unavailable. Please try again later.")

# Add this function to replace the one from the deleted commands.py file
def handle_authenticated_command(sender_id, text):
    """
    Process commands from authenticated users
    This is now handled by the process_facebook_message function
    """
    # This is just a placeholder that will redirect to process_facebook_message
    process_facebook_message(sender_id, text)

# Update the bot/conversation.py to use our new function
def update_conversation_module():
    """
    Update the conversation module to use our new function
    """
    try:
        logger.info("Updating conversation module...")
        # Import the module
        import bot.conversation

        # Replace the handle_incoming_message function
        bot.conversation.handle_incoming_message = process_facebook_message

        # Test if the replacement worked
        logger.info(f"Checking if replacement worked: {bot.conversation.handle_incoming_message.__name__}")

        logger.info("✅ Updated bot.conversation module to use integrated functionality")
    except Exception as e:
        logger.error(f"❌ Failed to update bot.conversation module: {e}")
        import traceback
        logger.error(traceback.format_exc())

def create_app():
    """Create and configure the Flask application"""
    logger.info("Creating Flask application...")
    app = Flask(__name__)

    # Register the messenger bot blueprint
    logger.info("Registering messenger bot blueprint...")
    app.register_blueprint(messenger_bot_blueprint)

    # Update the conversation module
    update_conversation_module()

    logger.info("✅ Flask application created and configured")
    return app

if __name__ == "__main__":
    app = create_app()
    port = int(os.getenv("PORT", "5000"))
    debug = os.getenv("FLASK_ENV") == "development"

    logger.info(f"Starting Flask server on port {port} (debug={debug})...")
    logger.info("==== ASLAA BOT READY ====")
    logger.info("The application is now handling both Facebook Messenger and MQTT communication")

    app.run(host="0.0.0.0", port=port, debug=debug)

def get_sim_info_with_timeout(device_number, token=None, timeout=10):
    """
    Get SIM information with a timeout.
    Sends the 'sim' command and waits for a response for up to 'timeout' seconds.
    Returns the response if received within the timeout, or None if timed out.
    """
    try:
        logger.info(f"Getting SIM info with {timeout}s timeout for device {device_number}")

        # Create a unique tracking ID for this command
        tracking_id = f"{device_number}:sim"

        # Check if we already have a pending command
        if tracking_id in command_tracking:
            logger.info(f"Command already in progress: {tracking_id}")
            # Use the existing command tracking
        else:
            # Send the command
            send_success = send_command_to_device(device_number, "sim", token)
            if not send_success:
                logger.error(f"Failed to send SIM command to device {device_number}")
                return None

        # Wait for the response with timeout
        start_time = time.time()
        while time.time() - start_time < timeout:
            # Check if we've received a response
            if tracking_id not in command_tracking:
                # Command has been processed and removed from tracking
                logger.info(f"Received SIM info response within timeout ({time.time() - start_time:.1f}s)")

                # Check if we have SIM data in the latest device data
                device_data = latest_device_data.get(device_number, {})
                if "balance" in device_data or "expiry" in device_data or "expiredDate" in device_data or "sms" in device_data:
                    # Format the response
                    message = f"📱 SIM картын мэдээлэл:\n\n"

                    # Add SIM status if available
                    if "status" in device_data:
                        message += f"Төлөв: {device_data['status']}\n"

                    # Add balance if available
                    if "balance" in device_data:
                        message += f"Үлдэгдэл: {device_data['balance']}₮\n"

                    # Add expiry if available
                    if "expiry" in device_data:
                        message += f"Хугацаа: {device_data['expiry']}\n"
                    elif "expiredDate" in device_data:
                        message += f"Хугацаа: {device_data['expiredDate']}\n"

                    # Add SMS text if available
                    if "sms" in device_data:
                        message += f"\nСМС: {device_data['sms']}\n"

                    return {
                        "success": True,
                        "data": device_data,
                        "message": message
                    }

                # If we don't have SIM data, return success but no data
                return {
                    "success": True,
                    "message": "SIM command completed but no data available"
                }

            # Sleep for a short time to avoid busy waiting
            time.sleep(0.1)

        # If we get here, the command timed out
        logger.warning(f"SIM info request timed out after {timeout}s")

        # Remove the command from tracking if it's still there
        if tracking_id in command_tracking:
            command_tracking.pop(tracking_id, None)

        return None

    except Exception as e:
        logger.error(f"Error getting SIM info with timeout: {e}")
        import traceback
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None

def check_sim_status(device_number, token=None):
    """
    Check the SIM card status for a device.
    First tries to get status via MQTT with a 10-second timeout.
    If that fails, tries the API as a fallback.
    If both fail, returns a "no sim information" message.
    """
    try:
        # First try to get status via MQTT with a 10-second timeout
        logger.info(f"Checking SIM status for device {device_number}")
        mqtt_result = get_sim_info_with_timeout(device_number, token, timeout=10)

        if mqtt_result and mqtt_result.get("success"):
            # MQTT command was successful, return its result
            logger.info(f"Successfully got SIM info via MQTT command")

            # Find Facebook users associated with this device and notify them
            for sender_id, session in user_sessions.items():
                if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                    send_message(sender_id, mqtt_result.get("message", "SIM картын мэдээлэл авагдлаа."))

            return mqtt_result

        # If MQTT timed out or failed, try API call as a fallback
        logger.info(f"MQTT command timed out or failed after 10 seconds, trying API for device {device_number}")
        api_result = check_sim_status_api(device_number, token)

        if api_result and api_result.get("success"):
            # API call was successful, return its result
            logger.info(f"Successfully got SIM info via API")

            # Find Facebook users associated with this device and notify them
            for sender_id, session in user_sessions.items():
                if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                    send_message(sender_id, api_result.get("message", "SIM картын мэдээлэл авагдлаа."))

            return api_result

        # Both MQTT and API failed
        logger.error("Both MQTT and API failed for SIM status check")

        # Notify users about the failure with the "no sim information" message
        for sender_id, session in user_sessions.items():
            if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                send_message(
                    sender_id,
                    "⚠️ SIM картын мэдээлэл байхгүй байна."
                )

        return {
            "success": False,
            "message": "No SIM information available"
        }
    except Exception as e:
        logger.error(f"Error checking SIM status: {e}")
        import traceback
        logger.error(f"Detailed error: {traceback.format_exc()}")

        # Notify users about the error
        for sender_id, session in user_sessions.items():
            if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                send_message(
                    sender_id,
                    "⚠️ SIM картын мэдээлэл байхгүй байна."  # Changed to match the requirement
                )

        return {
            "success": False,
            "message": f"Error checking SIM status: {str(e)}"
        }

def handle_logout(sender_id):
    """
    Handle user logout request by clearing their session and unsubscribing from device topics
    """
    try:
        # Get the current session
        session = user_sessions.get(sender_id, {})

        # Check if the user is authenticated
        if session.get("state") != "AUTHENTICATED":
            send_message(sender_id, "Та нэвтрээгүй байна.")
            return

        # Get the device number before clearing the session
        device_number = session.get("deviceNumber")

        # Unsubscribe from device topics if we have a device number
        if device_number and mqtt_client:
            try:
                # Unsubscribe from direct device topic
                mqtt_client.unsubscribe(f"{device_number}")
                # Unsubscribe from device/msg topic
                mqtt_client.unsubscribe(f"{device_number}/msg")
                logger.info(f"✅ Unsubscribed from device topics: {device_number} and {device_number}/msg")
            except Exception as e:
                logger.error(f"Failed to unsubscribe from device topics: {e}")

        # Clear the user's session
        user_sessions[sender_id] = {"state": "GREET"}

        # Send confirmation message
        send_message(sender_id, "Та амжилттай гарлаа. Дахин нэвтрэхийн тулд 'car' гэж бичнэ үү.")
        logger.info(f"User {sender_id} has logged out")

    except Exception as e:
        logger.error(f"Error during logout: {e}")
        import traceback
        logger.error(f"Detailed error: {traceback.format_exc()}")
        send_message(sender_id, "Гарах үед алдаа гарлаа. Дахин оролдоно уу.")

def provide_troubleshooting_advice(text, device_number=None, token=None):
    """
    Provide troubleshooting advice based on the user's message
    Note: device_number and token parameters are kept for compatibility but not used
    """
    text_lower = text.lower()

    # Check for common issues
    if any(keyword in text_lower for keyword in ["асахгүй", "not starting", "won't start"]):
        return (
            "🔧 Машин асахгүй байгаа бол дараах зүйлсийг шалгана уу:\n\n"
            "1. Машины аккумулятор цэнэгтэй эсэх\n"
            "2. Төхөөрөмжийн холболт зөв эсэх\n"
            "3. Машины түлхүүр зөв байрлалд эсэх\n\n"
            "Одоо машины төлөвийг шалгаж үзье..."
        )

    elif any(keyword in text_lower for keyword in ["холбогдохгүй", "not connecting", "offline"]):
        return (
            "📶 Төхөөрөмж холбогдохгүй байвал дараах зүйлсийг шалгана уу:\n\n"
            "1. SIM картын үлдэгдэл хангалттай эсэх\n"
            "2. Төхөөрөмжийн тэжээл холбогдсон эсэх\n"
            "3. Сүлжээний хамрах хүрээнд байгаа эсэх\n\n"
            "Одоо SIM картын төлөвийг шалгаж үзье..."
        )

    elif any(keyword in text_lower for keyword in ["түгжээ", "lock", "unlock"]):
        return (
            "🔒 Түгжих/түгжээ тайлах асуудал гарвал дараах зүйлсийг шалгана уу:\n\n"
            "1. Машины цахилгаан систем ажиллаж байгаа эсэх\n"
            "2. Төхөөрөмжийн холболт зөв эсэх\n"
            "3. Машины төлөвийг шалгах\n\n"
            "Одоо машины төлөвийг шалгаж үзье..."
        )

    # Default troubleshooting advice
    return (
        "🔧 Асуудлыг шийдвэрлэхийн тулд дараах зүйлсийг шалгана уу:\n\n"
        "1. Төхөөрөмжийн тэжээл холбогдсон эсэх\n"
        "2. SIM картын үлдэгдэл хангалттай эсэх\n"
        "3. Машины цахилгаан систем ажиллаж байгаа эсэх\n\n"
        "Одоо машины төлөвийг шалгаж үзье..."
    )

def check_specific_info_request(text, device_number, token=None):
    """
    Check if the user is asking for specific information
    Returns a dict with message and optional command to send
    Note: token parameter is kept for compatibility but not used
    """
    text_lower = text.lower()

    # SIM card related questions (including all forms of "negj")
    if any(keyword in text_lower for keyword in ["сим", "sim", "хугацаа", "дуусах", "үлдэгдэл", "balance", "негж", "negj"]):
        return {
            "message": "SIM картын мэдээллийг шалгаж байна...",
            "command": "sim"
        }

    # Temperature related questions
    elif any(keyword in text_lower for keyword in ["температур", "халуун", "хүйтэн", "temp", "temperature"]):
        # Check if we already have recent temperature data
        temp_info = get_specific_device_info(device_number, "temperature")
        if temp_info:
            return {
                "message": temp_info,
                "command": None  # No need to send command if we have recent data
            }
        else:
            return {
                "message": "Температурыг шалгаж байна...",
                "command": "check"
            }

    # Engine status related questions
    elif any(keyword in text_lower for keyword in ["асаалттай", "унтраалттай", "асаалт", "унтраалт", "engine", "status"]):
        # Check if we already have recent engine status data
        status_info = get_specific_device_info(device_number, "engine_status")
        if status_info:
            return {
                "message": status_info,
                "command": None  # No need to send command if we have recent data
            }
        else:
            return {
                "message": "Машины төлөвийг шалгаж байна...",
                "command": "check"
            }

    # Location related questions
    elif any(keyword in text_lower for keyword in ["хаана", "байршил", "location", "where", "haana"]):
        # Check if we already have recent location data
        location_info = get_specific_device_info(device_number, "location")
        if location_info:
            return {
                "message": location_info,
                "command": None  # No need to send command if we have recent data
            }
        else:
            return {
                "message": "Машины байршлыг шалгаж байна...",
                "command": "check"
            }

    # No specific information request detected
    return None

# In-memory storage for latest device data
latest_device_data = {}

# In-memory storage for command tracking
command_tracking = {}

def update_latest_device_data(device_id, data):
    """
    Update the latest device data for a device
    """
    if device_id not in latest_device_data:
        latest_device_data[device_id] = {}

    # Update the data
    latest_device_data[device_id].update(data)

    # Add a timestamp
    latest_device_data[device_id]["_last_updated"] = time.time()

    logger.info(f"Updated latest device data for {device_id}: {data}")

def get_specific_device_info(device_id, info_type):
    """
    Get specific information from the latest device data
    """
    if device_id not in latest_device_data:
        return None

    data = latest_device_data[device_id]

    # Check if data is too old (more than 5 minutes)
    if "_last_updated" in data and time.time() - data["_last_updated"] > 300:
        return None

    if info_type == "temperature":
        if "temp" in data:
            temp = data["temp"]
            try:
                temp_value = float(temp)
                if temp_value < 10:
                    return f"🌡️ Хүйтэн: {temp}°C"
                elif temp_value > 30:
                    return f"🌡️ Халуун: {temp}°C"
                else:
                    return f"🌡️ {temp}°C"
            except:
                return f"🌡️ {temp}°C"
        return None

    elif info_type == "location":
        if "Lat" in data and "Lon" in data:
            try:
                # Try to extract numeric values from coordinates
                lat_value = data["Lat"].split()[0] if isinstance(data["Lat"], str) else data["Lat"]
                lon_value = data["Lon"].split()[0] if isinstance(data["Lon"], str) else data["Lon"]

                # Format Google Maps link
                maps_link = f"https://www.google.com/maps?q={lat_value},{lon_value}"

                # Add additional info if available
                additional_info = []

                # Add engine status
                if "volt" in data:
                    try:
                        volt_value = float(data["volt"])
                        if volt_value > 13.5:
                            additional_info.append("🟢 Асаалттай")
                        else:
                            additional_info.append("🔴 Унтраалттай")
                    except:
                        pass

                # Add speed if moving
                if "Speed" in data and int(data["Speed"]) > 0:
                    additional_info.append(f"🚗 {data['Speed']} км/ц")

                # Combine all info
                if additional_info:
                    return f"📍 {maps_link}\n{' | '.join(additional_info)}"
                else:
                    return f"📍 {maps_link}"
            except Exception as e:
                logger.error(f"Error formatting location: {e}")
                # Fallback to basic format
                return f"📍 Lat: {data.get('Lat', 'N/A')}, Lon: {data.get('Lon', 'N/A')}"
        return None

    elif info_type == "engine_status":
        if "volt" in data:
            try:
                volt_value = float(data["volt"])
                if volt_value > 13.5:
                    return "🟢 Асаалттай"
                else:
                    return "🔴 Унтраалттай"
            except:
                pass
        return None

    elif info_type == "voltage":
        if "volt" in data:
            return f"⚡ {data['volt']}V"
        return None

    elif info_type == "speed":
        if "Speed" in data:
            try:
                speed_value = int(data["Speed"])
                if speed_value > 0:
                    return f"🚗 {speed_value} км/ц"
                else:
                    return "🚗 Зогсож байна"
            except:
                return f"🚗 {data['Speed']} км/ц"
        return None

    return None

def check_command_timeout(command_id):
    """
    Check if a command has timed out and take appropriate action
    """
    if command_id not in command_tracking:
        return

    command_info = command_tracking[command_id]

    # If we already received a response, do nothing
    if command_info["response_received"]:
        return

    device_number = command_info["device_number"]
    command = command_info["command"]
    token = command_info["token"]
    retry_count = command_info["retry_count"]

    logger.warning(f"⚠️ Command '{command}' to device {device_number} timed out after 10 seconds")

    # Maximum retry count
    MAX_RETRIES = 2  # This means we'll try 3 times total (original + 2 retries)

    if retry_count < MAX_RETRIES:
        # Try sending the command again
        logger.info(f"Retrying command '{command}' to device {device_number} (retry {retry_count + 1}/{MAX_RETRIES})")
        send_command_to_device(device_number, command, token, retry_count + 1)

        # If this was a critical command, also send a check command to verify device status
        if retry_count == 1 and command != "check" and command != "sim":
            logger.info(f"Sending check command to verify device status")
            send_command_to_device(device_number, "check", token, 0)  # Start with retry_count=0
    else:
        # We've reached the maximum number of retries
        logger.warning(f"⚠️ Command '{command}' failed after {MAX_RETRIES} retries. Stopping retry attempts.")

        # Check SIM status via API
        logger.info(f"Checking SIM status via API")
        check_sim_status_api(device_number, token)

        # Check device connectivity
        logger.info(f"Checking device connectivity")
        check_device_connectivity(device_number, token)

        # If we've tried multiple commands and they all failed, notify the user that the device is offline
        if command == "check":
            notify_device_offline(device_number)

    # Clean up the tracking entry
    command_tracking.pop(command_id, None)

def check_device_connectivity(device_number, token):
    """
    Check device connectivity by fetching logs
    """
    try:
        # Don't send a check command, just fetch logs
        logger.info(f"Checking connectivity for device {device_number} (logs only)")

        # Fetch logs
        from bot.utils import fetch_device_logs

        # Fetch recent logs
        logs = fetch_device_logs(device_number, token)

        if not logs:
            logger.warning(f"⚠️ No logs available for device {device_number}")
            # Notify users about the device being potentially offline
            for sender_id, session in user_sessions.items():
                if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                    send_message(
                        sender_id,
                        "⚠️ Төхөөрөмж сүлжээнд холбогдоогүй байна. Төхөөрөмжийг дахин асаах шаардлагатай."
                    )
            return

        # Check for connectivity issues in logs
        sim_issues = False
        connectivity_issues = False
        last_seen = None

        # Handle different log formats
        if isinstance(logs, list):
            # Logs are in list format
            for log in logs:
                if isinstance(log, dict):
                    log_text = log.get("message", "").lower()
                    log_time = log.get("createdAt")

                    # Track the most recent log time
                    if log_time and (not last_seen or log_time > last_seen):
                        last_seen = log_time

                    if "sim" in log_text and any(word in log_text for word in ["expired", "inactive", "no balance"]):
                        sim_issues = True

                    if any(word in log_text for word in ["offline", "disconnected", "no connection"]):
                        connectivity_issues = True
                elif isinstance(log, str):
                    # Log is a string
                    log_text = log.lower()
                    if "sim" in log_text and any(word in log_text for word in ["expired", "inactive", "no balance"]):
                        sim_issues = True

                    if any(word in log_text for word in ["offline", "disconnected", "no connection"]):
                        connectivity_issues = True
        elif isinstance(logs, dict):
            # Logs are in dict format
            for key, value in logs.items():
                if key == "lastSeen" or key == "createdAt":
                    last_seen = value
                elif isinstance(value, str) and ("sim" in value.lower() or "offline" in value.lower()):
                    log_text = value.lower()
                    if "sim" in log_text and any(word in log_text for word in ["expired", "inactive", "no balance"]):
                        sim_issues = True

                    if any(word in log_text for word in ["offline", "disconnected", "no connection"]):
                        connectivity_issues = True

        # Prepare messages based on issues found
        if sim_issues:
            logger.warning(f"⚠️ SIM card issues detected for device {device_number}")

            # Find Facebook users associated with this device and notify them
            for sender_id, session in user_sessions.items():
                if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                    send_message(
                        sender_id,
                        "⚠️ Таны төхөөрөмжийн SIM картын хугацаа дууссан эсвэл үлдэгдэл дууссан байж магадгүй. "
                        "SIM картын төлөвийг шалгана уу."
                    )

        if connectivity_issues:
            logger.warning(f"⚠️ Connectivity issues detected for device {device_number}")

            # Find Facebook users associated with this device and notify them
            for sender_id, session in user_sessions.items():
                if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                    send_message(
                        sender_id,
                        "⚠️ Таны төхөөрөмж сүлжээнд холбогдоогүй байна. Төхөөрөмжийн холболтыг шалгана уу."
                    )

    except Exception as e:
        logger.error(f"Error checking device connectivity: {e}")
        import traceback
        logger.error(f"Detailed error: {traceback.format_exc()}")

        # Still try to notify the user even if there was an error
        for sender_id, session in user_sessions.items():
            if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                send_message(
                    sender_id,
                    "⚠️ Төхөөрөмж одоогоор хариу өгөхгүй байна. Төхөөрөмжийг дахин асаах шаардлагатай."
                )

def is_license_extension_command(text):
    """
    Check if the text is a license extension command
    """
    extension_commands = ["extend", "сунгах", "sungah", "license", "лиценз"]
    return any(cmd in text.lower() for cmd in extension_commands)

def handle_license_extension(sender_id, text):
    """
    Direct handler for license extension commands
    """
    logger.info(f"Direct license extension handler called for user {sender_id} with text: {text}")
    from bot.authentication import handle_extend_license
    handle_extend_license(sender_id)

def handle_message(sender_id, message):
    """
    Handle incoming messages from users
    """
    # Get the message text
    text = message.get('text', '')

    # Check if this is a license extension command
    if is_license_extension_command(text):
        # Get the user's session
        session = user_sessions.get(sender_id, {})

        # If the user is authenticated, handle license extension
        if session.get("state") == "AUTHENTICATED":
            handle_license_extension(sender_id, text)
            return

    # Continue with normal message handling...

def check_sim_status_api(device_number, token=None):
    """
    Check SIM status via API only (used as a fallback)
    """
    try:
        logger.info(f"Checking SIM status via API for device {device_number}")

        # Get the base URL without trailing slash and without /api
        base_url = get_api_base_url().rstrip('/')
        if base_url.endswith('/api'):
            base_url = base_url[:-4]  # Remove /api suffix

        # Use the primary API endpoint
        url = f"{base_url}/api/device/sim-status?deviceNumber={device_number}"
        logger.info(f"Using SIM status API endpoint: {url}")

        headers = {"Authorization": f"Bearer {token}"} if token else {}
        logger.info(f"API request headers: {headers if not token else 'Authorization header set'}")

        # Make the API request with improved logging
        try:
            logger.info(f"Sending GET request to {url}")
            response = requests.get(url, headers=headers, verify=False, timeout=10)
            logger.info(f"API response status code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                logger.info(f"SIM status API response data: {data}")

                # Process the API response
                if data.get("success"):
                    sim_data = data.get("data", {})
                    logger.info(f"Successfully retrieved SIM data: {sim_data}")

                    # Format the response for the user
                    message = f"📱 SIM картын мэдээлэл:\n\n"

                    # Add SIM status if available
                    if "status" in sim_data:
                        message += f"Төлөв: {sim_data['status']}\n"

                    # Add balance if available
                    if "balance" in sim_data:
                        message += f"Үлдэгдэл: {sim_data['balance']}₮\n"

                    # Add expiry if available
                    if "expiry" in sim_data:
                        message += f"Хугацаа: {sim_data['expiry']}\n"
                    elif "expiredDate" in sim_data:
                        message += f"Хугацаа: {sim_data['expiredDate']}\n"

                    # Add warnings if needed
                    if "expired" in str(sim_data.get("status", "")).lower():
                        message += "\n⚠️ SIM картын хугацаа дууссан байна. Та дахин цэнэглэх шаардлагатай."
                    elif "low" in str(sim_data.get("balance", "")).lower() or int(str(sim_data.get("balance", "5000")).replace("₮", "")) < 1000:
                        message += "\n⚠️ SIM картын үлдэгдэл бага байна. Та дахин цэнэглэх шаардлагатай."

                    # Find Facebook users associated with this device and notify them
                    for sender_id, session in user_sessions.items():
                        if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                            logger.info(f"Sending SIM info to user {sender_id}")
                            send_message(sender_id, message)

                    return {
                        "success": True,
                        "data": sim_data,
                        "message": message
                    }
                else:
                    logger.warning(f"API returned success=false for SIM status: {data}")
            else:
                logger.warning(f"Failed to get SIM status via API. Status code: {response.status_code}")
                if hasattr(response, 'text'):
                    logger.warning(f"Response text: {response.text[:500]}")  # Log first 500 chars of response
        except requests.exceptions.RequestException as req_err:
            logger.error(f"Request error when calling SIM status API: {req_err}")

        # Notify users about the failure
        for sender_id, session in user_sessions.items():
            if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
                logger.info(f"Notifying user {sender_id} about SIM info failure")
                send_message(
                    sender_id,
                    "⚠️ SIM картын мэдээлэл байхгүй байна."
                )

        return {
            "success": False,
            "message": "Failed to get SIM status via API"
        }
    except Exception as e:
        logger.error(f"Error checking SIM status via API: {e}")
        import traceback
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return {
            "success": False,
            "message": f"Error checking SIM status via API: {str(e)}"
        }

def notify_device_offline(device_number):
    """
    Notify users that their device appears to be offline
    """
    # Check if we have SIM status information
    sim_info = None
    try:
        # Get the base URL without trailing slash and without /api
        base_url = get_api_base_url().rstrip('/')
        if base_url.endswith('/api'):
            base_url = base_url[:-4]  # Remove /api suffix

        # Use the primary API endpoint
        url = f"{base_url}/api/device/sim-status?deviceNumber={device_number}"
        logger.info(f"Getting SIM info for offline notification using API endpoint: {url}")

        # Find a token from any user with this device
        token = None
        for session in user_sessions.values():
            if session.get("deviceNumber") == device_number and session.get("token"):
                token = session.get("token")
                break

        headers = {"Authorization": f"Bearer {token}"} if token else {}
        logger.info(f"API request headers: {headers if not token else 'Authorization header set'}")

        try:
            response = requests.get(url, headers=headers, verify=False, timeout=10)
            logger.info(f"API response status code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                logger.info(f"SIM status API response for offline notification: {data}")

                if data.get("success"):
                    sim_info = data.get("data", {})
                    logger.info(f"Successfully retrieved SIM data for offline notification: {sim_info}")
            else:
                logger.warning(f"Failed to get SIM status for offline notification. Status code: {response.status_code}")
        except requests.exceptions.RequestException as req_err:
            logger.error(f"Request error when calling SIM status API for offline notification: {req_err}")
    except Exception as e:
        logger.error(f"Error getting SIM info for offline notification: {e}")
        import traceback
        logger.error(f"Detailed error: {traceback.format_exc()}")

    # Find Facebook users associated with this device
    for sender_id, session in user_sessions.items():
        if session.get("deviceNumber") == device_number and session.get("state") == "AUTHENTICATED":
            # Start with a basic message
            message = (
                "⚠️ Таны төхөөрөмж сүлжээнд холбогдоогүй байна. Дараах шалтгаанууд байж болно:\n\n"
                "1. Машин унтраалттай байгаа\n"
                "2. SIM картын хугацаа дууссан эсвэл үлдэгдэл дууссан\n"
                "3. Төхөөрөмж сүлжээний хамрах хүрээнээс гарсан\n\n"
                "Та дараах зүйлсийг шалгана уу:\n"
                "- Машинаа асаах\n"
                "- SIM картын үлдэгдлийг шалгах\n"
                "- Хэрэв асуудал хэвээр байвал техникийн тусламж авах (+976 99999999)"
            )

            # Add SIM information if available
            if sim_info:
                message += "\n\n📱 SIM картын мэдээлэл:\n"

                if "balance" in sim_info:
                    message += f"Үлдэгдэл: {sim_info['balance']}₮\n"

                if "expiredDate" in sim_info:
                    message += f"Хугацаа: {sim_info['expiredDate']}\n"
                elif "expiry" in sim_info:
                    message += f"Хугацаа: {sim_info['expiry']}\n"

                # Add warnings if needed
                if "balance" in sim_info and int(str(sim_info.get("balance", "5000")).replace("₮", "")) < 1000:
                    message += "\n⚠️ SIM картын үлдэгдэл бага байна. Та дахин цэнэглэх шаардлагатай."

            send_message(sender_id, message)

            # Log the notification
            logger.info(f"Notified user {sender_id} that device {device_number} appears to be offline")

def check_command_response(tracking_id):
    """
    Check if a command has received a response
    """
    if tracking_id not in command_tracking:
        return

    command_info = command_tracking.get(tracking_id)
    if not command_info:
        return

    # If we already received a response, do nothing
    if command_info.get("response_received", False):
        logger.info(f"Command {tracking_id} already received a response")
        return

    # Extract command details
    device_id, command = tracking_id.split(":", 1)

    # Check if we have received data from the device since the command was sent
    device_data = latest_device_data.get(device_id, {})
    command_time = command_info.get("timestamp", 0)
    data_time = device_data.get("_last_updated", 0)

    # If we've received data from the device after sending the command, consider it a response
    if data_time > command_time:
        logger.info(f"✅ Detected response for command '{command}' to device {device_id} based on timing")
        command_info["response_received"] = True

        # Format a response message based on the command and latest device data
        try:
            # Find Facebook users associated with this device
            for sender_id, session in user_sessions.items():
                if session.get("deviceNumber") == device_id and session.get("state") == "AUTHENTICATED":
                    # Format a response based on the command
                    if command == "as":
                        # Check if the engine is on based on voltage
                        try:
                            volt_value = float(device_data.get("volt", 0))
                            if volt_value > 13.5:
                                response_message = f"✅ Машин амжилттай асаалаа.\n🟢 Асаалттай | ⚡ Хүчдэл: {volt_value:.2f}V"
                            else:
                                response_message = f"⚠️ Машин асаагүй байж магадгүй.\n🔴 Унтраалттай | ⚡ Хүчдэл: {volt_value:.2f}V"
                        except:
                            response_message = "✅ Машины хариу ирлээ."
                    elif command == "untar":
                        # Check if the engine is off based on voltage
                        try:
                            volt_value = float(device_data.get("volt", 0))
                            if volt_value < 13.5:
                                response_message = f"✅ Машин амжилттай унтарлаа.\n🔴 Унтраалттай | ⚡ Хүчдэл: {volt_value:.2f}V"
                            else:
                                response_message = f"⚠️ Машин унтраагүй байж магадгүй.\n🟢 Асаалттай | ⚡ Хүчдэл: {volt_value:.2f}V"
                        except:
                            response_message = "✅ Машины хариу ирлээ."
                    elif command == "check":
                        # Format a status message with location and voltage
                        response_message = format_status_update(device_data)
                    else:
                        # Generic response for other commands
                        response_message = f"✅ '{get_friendly_command_name(command)}' команд амжилттай гүйцэтгэгдлээ."

                    # Send the message to the user
                    send_message(sender_id, response_message)
        except Exception as e:
            logger.error(f"Error formatting response message: {e}")
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")

        # Remove the command from tracking
        command_tracking.pop(tracking_id, None)
